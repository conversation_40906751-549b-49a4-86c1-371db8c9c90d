import js from '@eslint/js';
import pluginVue from 'eslint-plugin-vue';
import prettierConfig from 'eslint-config-prettier';

export default [
  js.configs.recommended,
  ...pluginVue.configs['flat/recommended'],
  prettierConfig,
  {
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        document: 'readonly',
        navigator: 'readonly',
        window: 'readonly',
        console: 'readonly',
        localStorage: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        alert: 'readonly',
        fetch: 'readonly',
        URL: 'readonly',
      },
    },
    files: ['**/*.{js,vue}'],
    ignores: [
      'dist/**',
      'node_modules/**',
      'coverage/**',
      'tests/e2e/reports/**',
      'playwright-report/**',
      'test-results/**',
      '*.config.js',
      '*.md',
      '*.svg',
      '*.json',
    ],
    rules: {
      // Vue specific rules
      'vue/multi-word-component-names': 'off', // Allow single word component names
      'vue/require-default-prop': 'off', // Don't require default values for props
      'vue/attribute-hyphenation': ['error', 'always'], // Use kebab-case for attributes
      'vue/v-on-event-hyphenation': ['error', 'always'], // Use kebab-case for event names

      // JavaScript rules
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_|isDarkMode|onMounted|computed|otpSent', // Ignore specific variables
      }], // Warn about unused variables with exceptions
      'prefer-const': 'warn', // Prefer const over let

      // Spacing and formatting
      'semi': ['error', 'always'], // Require semicolons
      'quotes': ['error', 'single', { avoidEscape: true }], // Use single quotes
      'comma-dangle': ['warn', 'always-multiline'], // Trailing commas in multiline
      'object-curly-spacing': ['warn', 'always'], // Spaces inside object literals
      'array-bracket-spacing': ['warn', 'never'], // No spaces inside array brackets
      'indent': ['warn', 2, { SwitchCase: 1 }], // 2 space indentation

      // ES6+ features
      'no-var': 'error', // No var, use let/const
      'prefer-template': 'warn', // Use template literals instead of string concatenation

      // Best practices
      'eqeqeq': ['warn', 'always'], // Use === instead of ==
      'curly': ['warn', 'all'], // Always use curly braces
      'no-param-reassign': ['warn', { props: false }], // Don't reassign function parameters
    },
  },
  {
    files: ['**/*.vue'],
    rules: {
      // Vue file specific overrides
    },
  },
  {
    files: ['**/*.js'],
    rules: {
      // JS file specific overrides
    },
  },
];
