import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url));

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue()
  ],
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true
  },
  base: '/',
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  assetsInclude: ['**/*.svg', '**/*.png'],
  server: {
    proxy: {
      '/api': {
        target: 'https://staging.rvmplus.com',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      },
      '/bitmap-api': {
        target: 'http://api2.rvmplus.com/api',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/bitmap-api/, ''),
      },
    }
  }
})
