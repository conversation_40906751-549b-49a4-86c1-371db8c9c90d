<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuth } from '../store/auth';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const { t } = useI18n();
const { user, currentPhone } = useAuth();
const { isDarkMode } = useTheme();

// Two-factor authentication state
const twoFactorEnabled = ref(false);
const toggleTwoFactor = () => {
  twoFactorEnabled.value = !twoFactorEnabled.value;
};

// Security notifications state
const securityNotificationsEnabled = ref(true);
const toggleSecurityNotifications = () => {
  securityNotificationsEnabled.value = !securityNotificationsEnabled.value;
};

// Dummy login history data
const loginHistory = ref([
  {
    id: 1,
    device: 'iPhone 13',
    location: 'Kuala Lumpur, MY',
    ip: '203.142.xx.xx',
    date: '2023-10-15 14:32',
    current: true,
  },
  {
    id: 2,
    device: 'Chrome on Windows',
    location: 'Petaling Jaya, MY',
    ip: '203.142.xx.xx',
    date: '2023-10-10 09:15',
    current: false,
  },
  {
    id: 3,
    device: 'Safari on Mac',
    location: 'Kuala Lumpur, MY',
    ip: '203.142.xx.xx',
    date: '2023-10-05 18:45',
    current: false,
  },
]);

// Change phone number function (dummy)
const changePhoneNumber = () => {
  // In a real app, this would open a flow to verify identity and change phone
  alert('This would start the phone number change process in a real app.');
};
</script>

<template>
  <div class="security-container">
    <!-- Phone Number Section -->
    <div class="section">
      <h2 class="section-title">{{ t('security.phoneNumber', 'Phone Number') }}</h2>
      <div class="card">
        <div class="phone-info">
          <div class="info-label">{{ t('security.currentPhone', 'Current Phone') }}</div>
          <div class="info-value">{{ currentPhone || '9876543210' }}</div>
        </div>
        <button class="action-button" @click="changePhoneNumber">
          {{ t('security.changePhone', 'Change Phone Number') }}
        </button>
      </div>
    </div>

    <!-- Two-Factor Authentication Section -->
    <div class="section">
      <h2 class="section-title">{{ t('security.twoFactor', 'Two-Factor Authentication') }}</h2>
      <div class="card toggle-card" @click="toggleTwoFactor">
        <div class="toggle-info">
          <div class="toggle-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
            </svg>
          </div>
          <div class="toggle-text">
            <div class="toggle-title">{{ t('security.twoFactorAuth', 'Two-Factor Authentication') }}</div>
            <div class="toggle-description">{{ t('security.twoFactorDescription', 'Receive a verification code when logging in from a new device') }}</div>
          </div>
        </div>
        <div class="toggle-switch" :class="{ 'active': twoFactorEnabled }">
          <div class="toggle-slider"></div>
        </div>
      </div>
    </div>

    <!-- Security Notifications Section -->
    <div class="section">
      <h2 class="section-title">{{ t('security.notifications', 'Security Notifications') }}</h2>
      <div class="card toggle-card" @click="toggleSecurityNotifications">
        <div class="toggle-info">
          <div class="toggle-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
          </div>
          <div class="toggle-text">
            <div class="toggle-title">{{ t('security.securityAlerts', 'Security Alerts') }}</div>
            <div class="toggle-description">{{ t('security.alertsDescription', 'Get notified about suspicious login attempts') }}</div>
          </div>
        </div>
        <div class="toggle-switch" :class="{ 'active': securityNotificationsEnabled }">
          <div class="toggle-slider"></div>
        </div>
      </div>
    </div>

    <!-- Login History Section -->
    <div class="section">
      <h2 class="section-title">{{ t('security.loginHistory', 'Login History') }}</h2>
      <div class="login-history-list">
        <div v-for="login in loginHistory" :key="login.id" class="login-history-item card">
          <div class="login-device">
            <strong>{{ login.device }}</strong>
            <span v-if="login.current" class="current-device-badge">{{ t('security.currentDevice', 'Current') }}</span>
          </div>
          <div class="login-details">
            <div class="login-location">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              {{ login.location }}
            </div>
            <div class="login-ip">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                <line x1="6" y1="6" x2="6.01" y2="6"></line>
                <line x1="6" y1="18" x2="6.01" y2="18"></line>
              </svg>
              {{ login.ip }}
            </div>
            <div class="login-time">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              {{ login.date }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.security-container {
  padding: 0;
  width: 100%;
  margin: 0 auto;
  padding-bottom: 7rem;
  background-color: #121212;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.section {
  width: 100%;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  color: white;
  border-bottom: 1px solid #333;
  margin-bottom: 1rem;
}

.card {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.phone-info {
  margin-bottom: 1rem;
}

.info-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
}

.action-button {
  width: 100%;
  background-color: #4ECDC4;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #3bb3aa;
}

.toggle-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.toggle-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.toggle-icon {
  margin-right: 1rem;
  color: var(--text-light);
}

.toggle-text {
  flex: 1;
}

.toggle-title {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.toggle-description {
  font-size: 0.85rem;
  color: var(--text-light);
}

.toggle-switch {
  width: 48px;
  height: 24px;
  background-color: #333;
  border-radius: 12px;
  position: relative;
  transition: background-color 0.3s;
}

.toggle-switch.active {
  background-color: #4ECDC4;
}

.toggle-slider {
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active .toggle-slider {
  transform: translateX(24px);
}

.login-history-list {
  padding: 0 1rem;
  width: 100%;
}

.login-history-item {
  padding: 1rem;
  margin-bottom: 0.75rem;
  background-color: #1e1e1e;
  border-radius: 8px;
  width: 100%;
}

.login-device {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  font-weight: 500;
}

.current-device-badge {
  background-color: #4ECDC4;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
}

.login-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.login-location,
.login-ip,
.login-time {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: var(--text-light);
}

.login-location svg,
.login-ip svg,
.login-time svg {
  margin-right: 0.5rem;
}

/* Media queries for responsive design */
@media (max-width: 480px) {
  .security-container {
    padding-bottom: 7rem; /* Ensure enough space for bottom navigation */
    padding-top: env(safe-area-inset-top, 0px);
    width: 100%;
  }

  .section {
    padding: 0;
    width: 100%;
  }

  .section-title {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #333;
    margin-bottom: 1rem;
    color: white;
  }

  .login-history-list {
    padding: 0 1rem;
    width: 100%;
  }

  .card {
    background-color: #1e1e1e;
    border-radius: 8px;
    width: 100%;
  }

  /* Keep the mobile design for login device */
  .login-device {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .current-device-badge {
    margin-top: 0;
  }
}

@media (min-width: 769px) {
  .security-container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    height: auto;
    padding-bottom: 2rem; /* Less padding needed on desktop */
    overflow-y: auto;
  }

  .section {
    width: 100%;
    max-width: 100%;
    margin: 0 0 1.5rem 0;
    padding: 0;
  }

  .section-title {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #333;
    margin-bottom: 1rem;
    color: white;
  }

  .login-history-list {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0 1rem;
  }

  .card {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background-color: #1e1e1e;
  }

  .toggle-card {
    background-color: #1e1e1e;
  }

  .login-history-item {
    background-color: #1e1e1e;
    margin-bottom: 0.5rem;
  }

  /* Fix login device display on web */
  .login-device {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .current-device-badge {
    margin-top: 0;
  }
}
</style>
