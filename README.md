# RVMPlus Rewards - Mobile Authentication

A Vue 3 application with mobile phone authentication, built with Vue Router and Vite.

## Features

- Mobile phone authentication with username/password
- Responsive design for mobile and desktop
- Vue Router for navigation
- Playwright tests for authentication flow

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm run test
```

## Deployment to Vercel

This project is configured for easy deployment to Vercel.

### Automatic Deployment

1. Push your code to a GitHub repository
2. Connect the repository to Vercel
3. Vercel will automatically detect the Vite configuration and deploy your app

### Manual Deployment

1. Install Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Login to Vercel:
   ```bash
   vercel login
   ```

3. Deploy from the project directory:
   ```bash
   vercel
   ```

4. For production deployment:
   ```bash
   vercel --prod
   ```

## Testing

For testing the login functionality, you can use these credentials:
- Phone: 60102431439
- Password: 123456
